import { UseMutationResult } from "@tanstack/react-query";
import React, { useEffect, useRef } from "react";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import { AskProps } from "../hooks/UseKiBotAskMutation.ts";
import { AskKiBotOnSuccessParams, OnErrorParams, User } from "../types/api.ts";
import { ChatMessage as chatMessageType } from "../types/chat.ts";
import ChatMessage from "./ChatMessage.tsx";
import LoadingSkeleton from "./LoadingSkeleton.tsx";

const Chat: React.FC<{ messages: chatMessageType[]; mutation: UseMutationResult<AskKiBotOnSuccessParams, OnErrorParams, AskProps>; user?: User;}> = ({ messages, mutation, user }) => {
  const containerRef = useRef<HTMLDivElement>(null);
  const {loadingMessages} = useChatMessageContext();

  console.log(messages)

  useEffect(() => {
    if (containerRef.current) {
      containerRef.current.scrollTop = containerRef.current.scrollHeight;
    }
   
  }, [messages]);


  return (
    <div
      ref={containerRef}
      className="flex h-full flex-col items-center gap-5 overflow-y-auto rounded bg-gray-100 p-3"
    >
      {messages?.map((message, index) => (
        <ChatMessage key={index} {...message} index={index} user={user} onRetry={()=> {mutation.mutate({question: messages[index - 1].children, productId: message.productId })}}/>
      ))}
      {loadingMessages && <LoadingSkeleton />}
    </div>
  );
};

export default Chat;
