import { useMutation } from "@tanstack/react-query";
import {
  <PERSON>ton,
  Link,
  ModalDialog,
  ModalDialogAction,
  ModalDialogCancel,
  ModalDialogContent,
  ModalDialogDescription,
  ModalDialogFooter,
  ModalDialogHeader,
  ModalDialogTitle,
  ModalDialogTrigger,
  Textarea,
  toast_success,
  Typography
} from "dbh-reactui";
import React, { useState } from "react";
import { KiBotService } from "../KiBotService.ts";
import { cn } from "../Utils.ts";
import StarFillLogo from "../assets/star-fill.svg?react";
import StarLogo from "../assets/star-line.svg?react";
import ThumbDownFillLogo from "../assets/thumb-down-fill.svg?react";
import ThumbDownLineLogo from "../assets/thumb-down-line.svg?react";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import { OnErrorParams, RateKiBotOnSuccessParams, RateKiBotProps, User } from "../types/api.ts";
import { Answer } from "../types/chat.ts";
import RatingDialogPopup from "./RatingDialogPopup.tsx";

type Props = { productId: string; conversationId: string; answerId: string, name:string, user?: User };

const RatingDialog: React.FC<Props> = ({ productId, conversationId, answerId, name, user }) => {
  const { savedChatMessages, setSavedChatMessages } = useChatMessageContext();
  const savedProductMessages = savedChatMessages[name];
  const chatMessage = savedProductMessages?.find(
    (msg): msg is Answer => msg.conversationId === conversationId && msg.dataId === answerId,
  );
  
  const [hoveredStars, setHoveredStars] = useState<number>(chatMessage?.rating ?? 0); // Sterne beim Hover
  const [selectedStars, setSelectedStars] = useState<number>(chatMessage?.rating ?? 0); // Sterne nach einem Klick
  const [comment, setComment] = useState<string>(chatMessage?.comment ?? "");
  const [rated, setRated] = useState<number | undefined >(chatMessage?.rating );

  function handleSuccess(answer: RateKiBotOnSuccessParams) {
    console.log(answer)
    setRated(selectedStars);
    setSavedChatMessages((prev) => {
      const list = prev[name] || [];
      return {
        ...prev,
        [name]: list.map((msg) =>
          msg.conversationId === conversationId && msg.dataId === answerId
            ? { ...msg, rating: selectedStars, comment: comment }
            : msg,
        ),
      };
    });
    toast_success("Erfolgreich bewertet.");
  }

  const mutation = useMutation<RateKiBotOnSuccessParams, OnErrorParams, RateKiBotProps>({
    mutationFn: KiBotService.rateKiBot,
    onSuccess: handleSuccess,
  });

  const renderStar = (index: number) => {
    const isFilled = index <= (hoveredStars || selectedStars);
    return (
      <div key={`star-${index}`}>
        {isFilled ? (
          <StarFillLogo
            className="h-20 w-20 cursor-pointer fill-yellow-400"
            onMouseEnter={() => setHoveredStars(index)} // Hover-Effekt
            onMouseLeave={() => setHoveredStars(0)} // Hover entfernen
            onClick={() => {
              if (index === selectedStars) {
                setSelectedStars(0);
              } else {
                setSelectedStars(index);
              }
            }} // Bewertung setzen
          />
        ) : (
          <StarLogo
            className="h-20 w-20 cursor-pointer fill-primary-100/50"
            onMouseEnter={() => setHoveredStars(index)} // Hover-Effekt
            onMouseLeave={() => setHoveredStars(0)} // Hover entfernen
            onClick={() => setSelectedStars(index)} // Bewertung setzen
          />
        )}
      </div>
    );
  };

  const renderRatedStars = () => {
    return (
      <div className="flex">
        {Array.from({ length: selectedStars }, (_, i) => {
          return <StarFillLogo key={i} className="h-4 w-4 fill-yellow-400" />;
        })}
      </div>
    );
  };

  const renderRating = () => {
    return (
      <div className="flex">
        {selectedStars < 0 ? <ThumbDownFillLogo className="h-4 w-4 fill-red-500" /> : renderRatedStars()}
      </div>
    );
  };

  return (
    <ModalDialog>
      {rated ? (
        <div className="flex gap-1 pr-2">
            <Typography variant="caption_2" className="text-primary-100">
              Bewertet:
            </Typography>
            {renderRating()}
        </div>
      ):(
<ModalDialogTrigger asChild>
       
          <Link className="w-full whitespace-nowrap pl-4 pr-2 text-end text-primary-100">
            Antwort bewerten
          </Link>
      </ModalDialogTrigger>
      )}
      <ModalDialogContent>
        <ModalDialogDescription className="hidden">Bewerten Sie Ihre Antwort</ModalDialogDescription>
        <ModalDialogHeader>
          <ModalDialogTitle className="text-center text-primary-100">
            Bewerten Sie die Antwort
          </ModalDialogTitle>
        </ModalDialogHeader>
        <div className="flex justify-center">{Array.from({ length: 5 }, (_, i) => renderStar(i + 1))}</div>
        <div className="flex justify-center">
          <Button
            variant="ghost"
            className={"flex gap-1"}
            onClick={() => {
              console.log("Clicked");
              if (selectedStars < 0) {
                setSelectedStars(0);
              } else {
                setSelectedStars(-1);
              }
            }}
          >
            {selectedStars < 0 ? (
              <ThumbDownFillLogo className="h-6 w-6 fill-red-500" />
            ) : (
              <ThumbDownLineLogo className="h-6 w-6 fill-primary-100" />
            )}
            <p className={cn(selectedStars < 0 ? "text-red-500" : "text-primary-100")}>Schlechte Antwort</p>
          </Button>
          <div className="flex items-center pl-1 font-bold"></div>
        </div>
        <div className="flex justify-end">
          <RatingDialogPopup />
        </div>
        <Textarea
          value={comment}
          onChange={(e) => setComment(e.target.value)}
          placeholder={"Kommentar (optional)"}
          className="flex h-full w-full border-none bg-primary-100 bg-opacity-10 p-3 pr-32 focus:outline-none"
        />
        <ModalDialogFooter>
          <ModalDialogCancel>Abbrechen</ModalDialogCancel>
          <ModalDialogAction
            onClick={() => {
              console.log("Hier wird bald das Rating an die KI verschickt...");
              const newRating: RateKiBotProps = {
                productId: productId,
                comment: comment,
                conversationId: conversationId,
                messageId: answerId,
                rating: selectedStars,
                user: user,
              };
              mutation.mutate(newRating);
            }}
          >
            Abschicken
          </ModalDialogAction>
        </ModalDialogFooter>
      </ModalDialogContent>
    </ModalDialog>
  );
};

export default RatingDialog;
