import { defineConfig } from "vite";
import react from "@vitejs/plugin-react";
import svgr from "vite-plugin-svgr";

// https://vite.dev/config/
export default defineConfig({
  plugins: [react(), svgr()],

  server: {
    proxy: {
      "/api": {
        target: "http://localhost:8061",//"https://kibot-dev.rancher.dev-ct.dbh.software",
        changeOrigin: true,
        secure: false,
      },
    },
  },
});
