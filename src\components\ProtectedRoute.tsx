import { useAuth } from "react-oidc-context";
import { Navigate } from "react-router-dom";

type ProtectedRouteProps = {
  children: JSX.Element;
};

export const ProtectedRoute = ({ children }: ProtectedRouteProps) => {
  const auth = useAuth();

  if (auth.isLoading) {
    return <div>Lädt...</div>; 
  }

  if (!auth.isAuthenticated) {
    return <Navigate to="/login" replace />; 
  }

  return children;
};
