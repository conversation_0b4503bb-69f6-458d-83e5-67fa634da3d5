import {
  TabsContent,
  Typography
} from "dbh-reactui";
import React, { useState } from "react";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import useKiBotAskMutation from "../hooks/UseKiBotAskMutation.ts";
import { User } from "../types/api.ts";
import Chat from "./Chat.tsx";
import ChatInput from "./ChatInput.tsx";

type ChatWindowProps = {
  productId: string;
  name: string
  user: User;
  conversationId: string
};

const ChatWindow: React.FC<ChatWindowProps> = ({ productId, name, user, conversationId }) => {
  const [question, setQuestion] = useState<string>("");
  const { mutation } = useKiBotAskMutation(productId, user, name, conversationId);

  const { savedChatMessages } = useChatMessageContext();

  const handleQuestionInputClick = () => {
    if (!question) return;

    mutation.mutate({ question, productId });
    setQuestion("");
  };

  const handleCommentSubmit = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      if (question.trim()) {
        mutation.mutate({ question, productId });
        setQuestion("");
      }
    }
  };


  return (
    <TabsContent
      value={conversationId}
      className={
        "relative flex h-full w-0 flex-col gap-3 data-[state=active]:flex data-[state=active]:h-[610px] data-[state=active]:max-h-[610px] data-[state=active]:w-full data-[state=active]:flex-col"
      }
    >
      <Chat messages={savedChatMessages[name]} mutation={mutation}  />
      <ChatInput
        value={question}
        isPending={mutation.isPending}
        onValueChange={setQuestion}
        onSubmit={handleCommentSubmit}
        onClick={handleQuestionInputClick}
      />
      <div className="flex rounded-md ">
        <Typography variant="caption_2_strong" className="text-gray-300">
          *Die Nachrichten werden von einer KI beantwortet und können fehlerhaft sein.
        </Typography>
      </div>
    </TabsContent>
  );
};

export default ChatWindow;
