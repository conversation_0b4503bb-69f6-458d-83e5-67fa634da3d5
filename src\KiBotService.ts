import { AskKiBotOnSuccessParams, AskKiBotProps, Conversation, ConversationsResponse, CreateConversationProps, GetConversationsProps, KiBotInfoOnSuccessParams, RateKiBotOnSuccessParams, RateKiBotProps, User } from "./types/api";
import { apiRequest } from "./utils/api";

function createMetadata(productId: string){
  return { language: "de", productId, target: productId, extend: "short" };
}

export class KiBotService { 
  static askKiBot(props: AskKiBotProps, signal?: AbortSignal) {

    console.log(props)
  return apiRequest<AskKiBotOnSuccessParams>({
    path: "/messages/ask",
    method: "POST",
    body: {
      metadata: createMetadata(props.productId),
      conversationId: props.conversationId,
      question: props.question,
      ...(props.user && {     // nur hinzufügen, wenn user existiert
        userId: props.user.userId,
      }),
    },
    signal,
  });
}

static extendQuestion(answerId: string){
  return apiRequest<AskKiBotOnSuccessParams>({
    path: "/messages/" + answerId +"/extend",
    method: "POST"
  })
}

   static rateKiBot(props: RateKiBotProps, signal?: AbortSignal) {
    return apiRequest<RateKiBotOnSuccessParams>({
      path: "/messages/" + props.messageId +"/rate",
      method: "POST",
      body: {
        metadata: createMetadata(props.productId),
        conversationId: props.conversationId,
        comment: props.comment,
        answerId: props.messageId,
        rating: props.rating,
      },
      signal,
    });
  }

   static getKiBotInfo(signal?: AbortSignal) {
    return apiRequest<KiBotInfoOnSuccessParams>({
      path: "/info",
      method: "GET",
      signal,
    });
  }

  static getConversationsForUser(props: GetConversationsProps): Promise<ConversationsResponse> {
  return apiRequest<ConversationsResponse>({
    path: `/conversations/${props.userId}`,
    method: "GET",
  });
}


  static createConversation(props: CreateConversationProps): Promise<Conversation> {
    return apiRequest<Conversation>({
      path: '/conversations',
      method: "POST",
      body: props
    })
  }

  static createOrUpdateUser(userBody: User): Promise<{userId: string, message: string}>{
    return apiRequest<{userId: string, message: string}>({
      path: "/users",
      method: "POST",
      body: userBody
    })
  }

static deleteOrClearConversation(
  clearOnly: boolean,
  conversationId: string,
  userId?: string
): Promise<{ message: string }> {  // <-- Typ hier festlegen
  const query = new URLSearchParams({
    clearOnly: String(clearOnly),
    ...(userId ? { userId } : {})
  })

  return apiRequest({
    path: `/conversations/${conversationId}?${query.toString()}`,
    method: 'DELETE'
  }) as Promise<{ message: string }>  // <-- Typ-Cast, wenn apiRequest generisch ist
}


static deleteMessage(dataId: string, userId?: string): Promise<{ message: string }> {
  const query = userId ? `?userId=${encodeURIComponent(userId)}` : "";
  return apiRequest<{ message: string }>({
    path: `/messages/${dataId}${query}`,
    method: "DELETE",
  });
}


}
