import React, { KeyboardEvent } from "react";
import SendLogo from "../assets/send.svg?react";
import { <PERSON><PERSON>, Lo<PERSON><PERSON>pin<PERSON>, Textarea } from "dbh-reactui";

type QuestionInputProps = {
  value: string;
  isPending: boolean;
  onValueChange: (value: string) => void;
  onSubmit: (event: KeyboardEvent<HTMLTextAreaElement>) => void;
  onClick: () => void;
};

const ChatInput: React.FC<QuestionInputProps> = ({ value, isPending, onValueChange, onSubmit, onClick }) => {
  return (
    <div className="relative flex h-36 w-full items-center gap-1">
      <Textarea
        value={value}
        disabled={isPending}
        onChange={(e) => onValueChange(e.target.value)}
        onKeyDown={onSubmit}
        placeholder={isPending ? "" : "Stellen Sie eine Frage..."}
        className="flex h-full w-full border-none bg-gray-100 p-3 pr-32 focus:outline-none"
      />
      <Button
        disabled={isPending}
        variant="primary"
        className="absolute bottom-0 right-0 flex h-[50px] min-w-[50px] items-center justify-center rounded-md"
        onClick={onClick}
        asIcon
      >
        {isPending ? (
          <LoadingSpinner size="small" type="long" className="flex h-10 w-10 text-white" />
        ) : (
          <SendLogo className={"flex h-full w-full"} />
        )}
      </Button>
    </div>
  );
};

export default ChatInput;
