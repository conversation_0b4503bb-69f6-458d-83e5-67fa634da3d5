import { toast_error, Toolt<PERSON>, <PERSON><PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Too<PERSON><PERSON><PERSON>rigger, <PERSON>po<PERSON> } from "dbh-reactui"
import type React from "react"
import { useState } from "react"
import ChatRemoveIcon from "../assets/delete-bin-line.svg?react"
import SendIcon from "../assets/send.svg?react"
import { useChatMessageContext } from "../context/chat-messages-context.ts"
import { useChatMutations } from "../hooks/UseChatMutations.ts"
import type { ChatMessage } from "../types/chat.ts"
import { cn } from "../Utils.ts"
import DeleteDialog from "./DeleteDialog.tsx"
import ErrorDialog from "./ErrorDialog.tsx"
import ErrorMessage from "./ErrorMessage.tsx"
import RatingDialog from "./RatingDialog.tsx"


type ChatMessageProps = ChatMessage & {
  onRetry : ()=> void
  index: number
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  productId,
  children,
  type,
  conversationId,
  dataId,
  createdAt,
  name,
  index,
  onRetry
}) => {
  const isQuestion: boolean = type === "question"
  const isError: boolean = type === "error"

  const {setSavedChatMessages } = useChatMessageContext()
  const [isOpenDelete, setOpenDelete] = useState(false)
  const [isOpenError, setOpenError] = useState(false)
  const {extendMutation, deleteMessagesMutation} = useChatMutations({dataId: dataId!, productId, conversationId, name})
 
  async function retryAskAndDelete() {
  try {

    setSavedChatMessages((prev) => {
      const updated = { ...prev }

      if (updated[name]?.length) {
        updated[name] = updated[name].filter((_, i) => i !== index)
      }

      return updated
    })
    const answer = await onRetry();

    return answer;
  } catch (err) {
    console.error("Retry failed:", err);
    toast_error("Retry fehlgeschlagen.");
  }
}

type TooltipAction = {
  label: string
  icon: React.FC<React.SVGProps<SVGSVGElement>>
  onClick: () => void
}

const tooltipActions: TooltipAction[] = [
  {
    label: "Löschen",
    icon: ChatRemoveIcon,
    onClick: () => setOpenDelete(true),
  },
  !isQuestion && {
    label: "Erweitern",
    icon: SendIcon,
    onClick: () => extendMutation.mutate({ dataId: dataId! }),
  },
].filter(Boolean) as TooltipAction[]
  


 const triangle = (
  <div
    className={cn(
      "h-0 w-0 border-t-[15px] border-l-transparent border-r-transparent",
      isQuestion
        ? "border-l-[15px] border-t-white"
        : cn(
            "border-r-[15px]",
            isError ? "border-t-error border-opacity-30" : "border-t-primary-100 border-opacity-50",
          ),
    )}
  />
)

  return (
    <TooltipProvider>
      <Tooltip>
        <div className={cn("flex w-full", (isQuestion || isError) && "justify-end")}>
          <div className="flex max-w-[475px] flex-col">
            {!isError && (
              <TooltipTrigger asChild>
                <div
                  className={cn(
                    "flex rounded-t-xl p-2",
                    isQuestion
                      ? "bg-white"
                      : cn(isError ? "bg-error bg-opacity-20" : "rounded-r-xl bg-primary-100 bg-opacity-50"),
                  )}
                >
                  <Typography variant="body_1">{children}</Typography>
                </div>
              </TooltipTrigger>
            )}
            
            {!isError && (
            <div className={cn("flex w-full justify-between", isQuestion && "text-end")}>
              {isQuestion ? null : triangle}
              <Typography variant="caption_2" className="w-full text-gray-300">
                {createdAt}
              </Typography>
              <div className="flex items-center">
                {type === "answer" && conversationId && dataId ? (
                  <RatingDialog
                    name={name}
                    productId={productId}
                    conversationId={conversationId}
                    answerId={dataId}
                  />
                ) : null}
                {isQuestion ? triangle : null}
                <TooltipContent>
                 {tooltipActions.map(({ label, icon: Icon, onClick }) => (
                    <button key={label} onClick={onClick} className="flex items-center space-x-2 group">
                      <Icon className="h-4 w-4 fill-primary-100 group-hover:fill-error transition-colors" />
                      <Typography variant="subtitle_2" className="text-primary-100 group-hover:text-error transition-colors">
                        {label}
                      </Typography>
                    </button>
                  ))}
                  
                </TooltipContent>
              </div>
            </div>
            )}
            

           
            {isError && (
              <ErrorMessage 
                setOpenError={setOpenError}  
                onDelete={() => {
                   setSavedChatMessages((prev) => {
                      const updated = { ...prev }

                      if (updated[name]?.length) {
                        updated[name] = updated[name].filter((_, i) => i !== index)
                      }

                      return updated
                    })
                }}
                onRetry={retryAskAndDelete}
          />
            )}
          </div>
        </div>
      </Tooltip>

      <DeleteDialog isOpen={isOpenDelete} onOpenChange={setOpenDelete} dataId={dataId!}  onDelete={(id) =>
          deleteMessagesMutation.mutate({ dataId: id, deleteType: isQuestion? "questionBlock" : "single" })
        }/>
      <ErrorDialog
        isOpen={isOpenError}
        onOpenChange={setOpenError}
        errorMessage={String(children)}
      />
    </TooltipProvider>
  )
}

export default ChatMessage
