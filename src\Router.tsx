import { BrowserRouter, Routes, Route } from "react-router";
import App from "./App";
import { LoginPage } from "./pages/login";
import { ProtectedRoute } from "./components/ProtectedRoute";

export const Router = () => {
  return (
    <BrowserRouter >
      <Routes>
        <Route path="/login" element={<LoginPage />} />
        <Route
          path="/*"
          element={
            <ProtectedRoute>
              <App />
            </ProtectedRoute>
          }
        />
      </Routes>
    </BrowserRouter>
  );
};
