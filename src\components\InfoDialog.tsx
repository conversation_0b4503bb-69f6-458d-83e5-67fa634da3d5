import { useQuery } from "@tanstack/react-query";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  Typography
} from "dbh-reactui";
import React from "react";
import { KiBotService } from "../KiBotService.ts";
import { KiBotInfoOnSuccessParams } from "../types/api.ts";

interface InfoDialogProps {
  dialogOpen: boolean
  onOpenChange: (state: boolean) => void
}

export const InfoDialog: React.FC<InfoDialogProps> = ({dialogOpen, onOpenChange}) => {
  const { data } = useQuery<KiBotInfoOnSuccessParams>({
    queryKey: ["kiBotInfoQuery"],
    queryFn: ()=> KiBotService.getKiBotInfo(),
    initialData: { build_timestamp: "-", version: "-", service: "-" },
    refetchInterval: 30000,
  });

  return (
    <Dialog open={dialogOpen} onOpenChange={()=>onOpenChange(!dialogOpen)}>
           <DialogContent className="flex w-full flex-col gap-1 focus-visible:outline-none">
              <DialogHeader>
                <DialogTitle>System Informations</DialogTitle>
              </DialogHeader>
              <div className="flex gap-2">
                <Typography variant={"caption_1_stronger"}>Chatbot-Version:</Typography>
                <Typography variant={"caption_1"}>{import.meta.env.VITE_VERSION}</Typography>
              </div>
              <div className="flex gap-2">
                <Typography variant={"caption_1_stronger"}>Chatbot-Build-Datum:</Typography>
                <Typography variant={"caption_1"}>{import.meta.env.VITE_VERSION_BUILD_TIMESTAMP}</Typography>
              </div>
              <div className="flex gap-2">
                <Typography variant={"caption_1_stronger"}>Backend-Version:</Typography>
                <Typography variant={"caption_1"}>{data?.version}</Typography>
              </div>
              <div className="flex gap-2">
                <Typography variant={"caption_1_stronger"}>Backend-Build-Datum:</Typography>
                <Typography variant={"caption_1"}>{data.build_timestamp}</Typography>
              </div>
            </DialogContent>
      </Dialog>
  );
};
