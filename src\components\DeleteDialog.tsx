import { ModalDialog, ModalDialogAction, ModalDialogCancel, ModalDialogContent, ModalDialogDescription, ModalDialogFooter, ModalDialogHeader, ModalDialogTitle } from "dbh-reactui";


interface DeleteDialogProps{
    isOpen: boolean;
    onOpenChange: (isOpen: boolean)=>void;
    dataId: string;
    onDelete: (id: string)=> void
}
const DeleteDialog = ({isOpen, onOpenChange, dataId, onDelete}: DeleteDialogProps)=>{


    return (
        <ModalDialog open={isOpen} onOpenChange={()=>onOpenChange(!isOpen)}>
            <ModalDialogContent>
                <ModalDialogHeader>
                    <ModalDialogTitle>Sicher das Sie die Nachricht löschen wollen?</ModalDialogTitle>
                    <ModalDialogDescription>Achtung! <PERSON><PERSON> l<PERSON> von <PERSON>, werden alle zugehörigen Antworten gelöscht!</ModalDialogDescription>
                </ModalDialogHeader>
            
           <ModalDialogFooter>
                     <ModalDialogCancel>Abbrechen</ModalDialogCancel>
                     <ModalDialogAction
                       onClick={()=>onDelete(dataId)}
                     >
                       Löschen
                </ModalDialogAction>
            </ModalDialogFooter>
            </ModalDialogContent>
        </ModalDialog>
    )
}

export default DeleteDialog