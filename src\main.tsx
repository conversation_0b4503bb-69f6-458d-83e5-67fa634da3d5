import { StrictMode } from "react";
import { createRoot } from "react-dom/client";

import "dbh-reactui-tokens/css/normalize/normalize.css";
import "dbh-reactui-tokens/css/tokens/tokens.css";
import "dbh-reactui-tokens/css/themes/light/light.css";
import "dbh-reactui-tokens/css/themes/dark/dark.css";
import "./index.css";
import { QueryClientProvider } from "@tanstack/react-query";
import { ReactQueryDevtools } from "@tanstack/react-query-devtools";
import ChatMessagesProvider from "./provider/chat-messages-provider.tsx";
import { AuthProvider, AuthProviderProps } from "react-oidc-context";
import { queryClient } from "./utils/queryClient.ts";
import { Router } from "./Router.tsx";




function trimStateFromURL(url: string) {
  if (url.includes("&state=")) {
    const stateIndex = url.lastIndexOf("&state=");
    return url.substring(0, stateIndex);
  } else {
    return "";
  }
}

const oidcSettings: AuthProviderProps = {
  authority: import.meta.env.VITE_OIDC_AUTHORITY,
  client_id: import.meta.env.VITE_OIDC_CLIENT_ID,
  redirect_uri: window.location.origin,
  post_logout_redirect_uri: window.location.origin,
  response_type: "code",
  scope: import.meta.env.VITE_OIDC_SCOPE,
  automaticSilentRenew: true,
  onSigninCallback: () =>
    window.history.replaceState(
      {},
      document.title,
      window.location.pathname + trimStateFromURL(window.location.search),
    ),
};

createRoot(document.getElementById("root")!).render(
  <StrictMode>
    <AuthProvider {...oidcSettings}>
      <QueryClientProvider client={queryClient}>
        <ChatMessagesProvider>
          <ReactQueryDevtools initialIsOpen={false} />
          <Router />
        </ChatMessagesProvider>
      </QueryClientProvider>
    </AuthProvider>
  </StrictMode>,
);
