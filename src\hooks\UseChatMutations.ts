import { useMutation } from "@tanstack/react-query";
import { AskKiBotOnSuccessParams, DeleteConversationProps, OnErrorParams, User } from "../types/api";
import { KiBotService } from "../KiBotService";
import { useChatMessageContext } from "../context/chat-messages-context";
import { toast_error, toast_success } from "dbh-reactui";
import { ChatMessage } from "../types/chat";
import { getCurrentFormattedDate } from "../DateUtils";


export function useChatMutations({dataId, productId, conversationId, name, user}: {dataId: string | undefined, productId: string, conversationId: string, name: string, user?: User}) {
  const { savedChatMessages, setSavedChatMessages, setLoadingMessages } = useChatMessageContext()

  const deleteMessagesMutation = useMutation<
    { message: string },
    OnErrorParams,
    { dataId: string; deleteType: "single" | "questionBlock" }
  >({
    mutationFn: ({ dataId }) => KiBotService.deleteMessage(dataId),
    onSuccess: (data, variables) => {
      toast_success(data.message)

      setSavedChatMessages((prev) => {
        const updated = { ...prev }
        console.log(variables.deleteType)
        for (const convId in updated) {
          if (variables.deleteType === "single") {
            updated[convId] = updated[convId].filter(
              (msg) => msg.dataId !== variables.dataId,
            )
          } else if (variables.deleteType === "questionBlock") {
           
            updated[convId] = updated[convId].filter((m) => m.dataId !== variables.dataId)
             updated[convId] = updated[convId].filter((m) => m.questionId !== variables.dataId)
            
          }
        }

        return updated
      })
    },
    onError: (error) => console.error(error),
  })


    const extendMutation = useMutation<
      AskKiBotOnSuccessParams,
      OnErrorParams,
      {dataId: string}
    >({
      mutationFn: ()=> KiBotService.extendQuestion({answerId: dataId!, user}),
      onMutate: () => {
      setLoadingMessages(true);
      },
      onSettled: () => {
        setLoadingMessages(false);
      },
      onSuccess: (data)=>{
        const newAnswers: ChatMessage[] = data.answers.map((ans, index) => ({
              productId,
              type: "answer",
              conversationId: data.conversationId,
              dataId: ans.answerId,
              createdAt: getCurrentFormattedDate(),
              children: ans.answerText,
              messageId: (savedChatMessages[name]?.length || 0) + index,
              language: "de",
              name,
            }));
        
            setSavedChatMessages((prev) => ({
              ...prev,
              [name]: [...(prev[name] || []), ...newAnswers],
            }));
      },
      onError: (error) =>{
        console.error("KiBot API Error:", error.message);
      toast_error(error.message);
  
      const newError: ChatMessage = {
        productId,
        type: "error",
        createdAt: getCurrentFormattedDate(),
        children: error.message,
        language: "de",
        messageId: savedChatMessages[name]?.length || 0,
        name,
        conversationId,
      };
  
      setSavedChatMessages((prev) => ({
        ...prev,
        [name]: [...(prev[name] || []), newError],
      }));
      }
    })


    const deleteOrClearChatMutation = useMutation<
      { message: string },
      OnErrorParams,
      DeleteConversationProps
    >({
      mutationFn: ({ clearOnly, conversationId, userId }) =>
        KiBotService.deleteOrClearConversation(clearOnly, conversationId, userId),
      onSuccess: (data, variables) => {
        const { clearOnly} = variables;
    
        setSavedChatMessages((prev) => {
          //console.log(prev)
          const newState = { ...prev };
          if (!clearOnly) {
            delete newState[name];
          } else if (newState[name] && newState[name].length > 0) {
         
            newState[name] = [newState[name][0]];
          }
          return newState;
        });
    
        toast_success(data.message);
      },
    });


    return {deleteMessagesMutation, deleteOrClearChatMutation, extendMutation}
}