import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List } from "dbh-reactui";
import { useEffect, useRef } from "react";
import { useAuth } from "react-oidc-context";
import LoginIcon from "../assets/login-box-line.svg?react";
import DEBBI from "../assets/robot-3-line.svg?react";
import UserRegister from "../assets/user-line.svg?react";

export const LoginPage = () => {
  const auth = useAuth();
  const loginRef = useRef<HTMLDivElement>(null);
  const registerRef = useRef<HTMLDivElement>(null);

  const handleLogin = () => auth.signinRedirect();
  const handleRegister = () => auth.signinRedirect();

  useEffect(() => {
    const updateTabIndex = (el: HTMLDivElement | null) => {
      if (!el) return;
      const isActive = el.getAttribute("data-state") === "active";
      el.querySelectorAll<HTMLElement>('button, [tabindex]:not([tabindex="-1"])').forEach(
        (e) => (e.tabIndex = isActive ? 0 : -1),
      );
    };
    updateTabIndex(loginRef.current);
    updateTabIndex(registerRef.current);
  }, []);

  return (
    <div className="bg-background flex min-h-screen w-full">
      <div className="from-primary via-primary relative hidden flex-col justify-between overflow-hidden bg-gradient-to-br to-accent p-12 md:flex md:w-1/2">
        <div className="absolute inset-0 opacity-10">
          <div className="bg-primary-foreground absolute right-0 top-0 h-96 w-96 rounded-full blur-3xl"></div>
          <div className="absolute bottom-0 left-0 h-72 w-72 rounded-full bg-accent blur-3xl"></div>
        </div>

        <div className="relative z-10 flex flex-col gap-8">
          <div className="group flex cursor-pointer items-center gap-3">
            <div className="bg-primary-foreground flex h-12 w-12 transform items-center justify-center rounded-lg shadow-lg transition-shadow duration-300 group-hover:scale-105 group-hover:shadow-xl">
              <DEBBI />
            </div>
            <h1 className="text-primary-foreground text-3xl font-bold tracking-tight">DEBBIE</h1>
          </div>

          <div className="mt-12 space-y-6">
            <h2 className="text-primary-foreground text-balance text-5xl font-bold leading-tight">
              Willkommen bei DEBBIE
            </h2>
            <p className="text-primary-foreground/80 max-w-sm text-lg leading-relaxed">
              Werden Sie effektiver mit unsererm neuen Chat Bot DEBBIE
            </p>
          </div>
        </div>

        <div className="relative z-10 space-y-2">
          <p className="text-primary-foreground/60 text-sm">© 2025 DEBBIE. Alle Rechte vorbehalten.</p>
        </div>
      </div>

      <div className="from-background to-background/95 flex w-full flex-col justify-center bg-gradient-to-b px-6 py-12 sm:px-12 md:w-1/2">
        <div className="mx-auto w-full max-w-md">
          <Tabs defaultValue="login">
            <TabsList className="border-border/50 mb-8 flex justify-center gap-1 border-b">
              <Tab value="login">
                <div ref={loginRef} className="flex items-center gap-2">
                  <LoginIcon className="h-5 w-5" />
                  Anmelden
                </div>
              </Tab>
              <Tab value="register">
                <div ref={registerRef} className="flex items-center gap-2">
                  <UserRegister className="h-5 w-5" />
                  Registrieren
                </div>
              </Tab>
            </TabsList>

            <TabsContent value="login">
              <Button
                onClick={handleLogin}
                className="from-primary hover:shadow-primary/20 mt-6 h-11 w-full transform rounded-lg bg-gradient-to-r to-accent font-semibold transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg"
              >
                Anmelden
              </Button>
            </TabsContent>
            <TabsContent value="register">
              <Button
                onClick={handleRegister}
                className="from-primary hover:shadow-primary/20 mt-6 h-11 w-full transform rounded-lg bg-gradient-to-r to-accent font-semibold transition-all duration-200 hover:-translate-y-0.5 hover:shadow-lg"
              >
                Registrieren
              </Button>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  );
};
