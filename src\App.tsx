import {
  <PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>alog<PERSON>ontent,
  <PERSON><PERSON><PERSON>ooter,
  <PERSON><PERSON>Header,
  DialogTitle,
  DialogTrigger,
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  InputWithLabel,
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
  Tab,
  Tabs,
  TabsList,
  toast_error,
  Toaster,
  Typography
} from "dbh-reactui";
import React, { useEffect, useMemo, useState } from "react";
import { useAuth } from "react-oidc-context";
import ChatAddIcon from "./assets/add-line.svg?react";
import ChatRemoveIcon from "./assets/delete-bin-line.svg?react";
import ChatMenuIcon from "./assets/more-2-fill.svg?react";
import AccountDropdown from "./components/AccountDropdown.tsx";
import ChatWindow from "./components/ChatWindow.tsx";
import { InfoDialog } from "./components/InfoDialog.tsx";
import { useChatMessageContext } from "./context/chat-messages-context.ts";
import { useChatMutations } from "./hooks/UseChatMutations.ts";
import { KiBotService } from "./KiBotService.ts";
import { User } from "./types/api.ts";
import { ChatMessageUtil } from "./utils/SavedChatMessageUtil.ts";

type ChatEntry = {
  name: string;
  productId: string;
  conversationId: string;
};

type ChatDropdownMenuProps = {
  chat: ChatEntry;
  user: User;
};

const ChatDropdownMenu: React.FC<ChatDropdownMenuProps> = ({ chat, user }) => {
  const { deleteOrClearChatMutation } = useChatMutations({
    dataId: undefined,
    productId: chat.productId,
    conversationId: chat.conversationId,
    name: chat.name
  });

  const handleDeleteOrClearConversation = (clearOnly: boolean) => {
    deleteOrClearChatMutation.mutate({
      clearOnly,
      conversationId: chat.conversationId,
      userId: user.userId
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant={"ghost"} asIcon>
          <ChatMenuIcon className={"h-3 w-3"} />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent>
        <DropdownMenuItem
          className={"gap-1"}
          onClick={() => handleDeleteOrClearConversation(false)}
        >
          <ChatRemoveIcon className={"h-4 w-4"} />
          <span>Delete Chat</span>
        </DropdownMenuItem>
        <DropdownMenuItem
          className={"gap-1"}
          onClick={() => handleDeleteOrClearConversation(true)}
        >
          <ChatRemoveIcon className={"h-4 w-4"} />
          <span>Clear Chat</span>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

function App() {
  const auth = useAuth();

  const user: User = useMemo(() => {

  const p = auth.user?.profile;
  const user = {
    userId: p?.sub ?? "",
    username: p?.preferred_username ?? "Guest",
    email: p?.email ?? "",
    emailVerified: p?.email_verified ?? false,
    name: p?.name ?? "",
    firstname: p?.given_name ?? "",
    lastname: p?.family_name ?? "",
    language: ""
  };

  console.log(user)

  return user
}, [auth.user]);


  useEffect(() => {
    document.title = import.meta.env.VITE_NAME;
  }, []);

  const getStage = () => {
    const hostname = window.location.hostname;
    const hostnameElements = hostname.split(".");
    if (hostnameElements.length > 0) {
      const serviceHostname = hostnameElements[0];
      const serviceHostnameElements = serviceHostname.split("-");
      if (serviceHostnameElements.length <= 1) {
        if (serviceHostnameElements[0] === "localhost") {
          return "LOCAL";
        }
      }
      return serviceHostnameElements[serviceHostnameElements.length - 1].toUpperCase();
    }
    return "";
  };

  const getBotNames = (): string[] => {
    const raw = import.meta.env.VITE_BOT_NAMES ?? "";
    return raw
      .split(",")
      .map((s: string) => s.trim())
      .filter(Boolean);
  };

  const { savedChatMessages, setSavedChatMessages } = useChatMessageContext();

  const [chats, setChats] = useState<ChatEntry[]>([]);

  const [selectedNewChat, setSelectedNewChat] = useState<string>("");

  const [newChatDialogIsOpen, setNewChatDialogIsOpen] = useState<boolean>(false);
  const [dialogOpen, setDialogOpen] = useState(false)
  const [chatName, setChatName] = useState<string>("")

useEffect(() => {
  console.log(savedChatMessages)
  const keys = Object.keys(savedChatMessages);
  const chatObjects = keys.map((key) => {
    const firstMessage = savedChatMessages[key]?.[0];
    return {
      name: key,
      productId: firstMessage?.productId ?? "",
      conversationId: firstMessage?.conversationId ?? ""
    };
  });

  console.log(chatObjects)

  setChats(chatObjects);
}, [savedChatMessages]);

  const renderTabs = () => {
    return chats.map((chat, index) => {
      return (
        <Tab value={chat.conversationId}  key={index}  asChild>
          <div className="flex flex-col justify-between items-center w-full"> 
            <span>{chat.name.toUpperCase()}</span>
            <ChatDropdownMenu
              chat={chat}
              user={user}
            />
          </div>
         
        </Tab>
      );
    });
  };

  const renderTabsContent = () => {
    return chats.map((chat, index) => {
      return <ChatWindow conversationId={chat.conversationId} productId={chat.productId} name={chat.name}  user={user} key={index} />;
    });
  };

  const renderStage = () => {
    return (
      <Typography variant="caption_1_strong" className="flex rounded-md bg-warning p-1">
        {getStage()}
      </Typography>
    );
  };

const handleAddBot = async () => {
  if (selectedNewChat && !chats?.some(chat => chat.name == chatName)) {
    if (!chatName) {
      toast_error("Du hast keinen Namen verteilt!");
      return;
    }

    const newChatBody = {
      userId: user.userId,
      productId: selectedNewChat,
      name: chatName,
    };

    try {
      const newConversation = await KiBotService.createConversation(newChatBody);

      const transformedConversation = ChatMessageUtil.transformConversationToSavedMessages(newConversation)
      setSavedChatMessages((prev)=> {
        const updated = {...prev};
        updated[newChatBody.name] = transformedConversation

        return updated
      })
      setChats((prevChats) => [
        ...prevChats,
        { name: newChatBody.name, productId: newChatBody.productId, conversationId: newConversation.conversationId },
      ]);

    } catch {
      toast_error("Fehler beim Erstellen der Conversation");
    }
  } else {
    toast_error(
      "Chat für das Produkt " + selectedNewChat.toUpperCase() + " existiert bereits!",
    );
  }
  setNewChatDialogIsOpen((prevState) => !prevState);
};
  return (
    <div className="flex h-screen w-full items-center justify-center bg-primary-100 bg-opacity-70">
      <Card className="flex h-[700px] w-[900px]">
        <CardHeader className="flex w-full items-center justify-between">
          <div className="flex items-center gap-2">
            <CardTitle>{import.meta.env.VITE_NAME}</CardTitle>
            {renderStage()}
          </div>
          <AccountDropdown onDialogTrigger={setDialogOpen} />
        </CardHeader>
        <CardContent className="flex h-full w-full flex-col">
          <Tabs orientation="vertical" defaultValue="alpo" className="flex">
            <TabsList
              orientation={"vertical-left"}
              tabProps={{ labelPosition: "left" }}
              className="mr-2 w-64"
            >
              <div className={"mt-2 px-2 flex justify-between"}>
                <Typography variant={"subtitle_1"} className={"mb-6"}>
                  Bots
                </Typography>
                <Dialog open={newChatDialogIsOpen} onOpenChange={setNewChatDialogIsOpen}>
                  <DialogTrigger asChild>
                    <Button
                      asIcon
                      variant={"outlined"}
                      className={"h-8 w-5"}
                      onClick={() => setNewChatDialogIsOpen((prevState) => !prevState)}
                    >
                      <ChatAddIcon className={"h-5 w-5"} />
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Add new Bot</DialogTitle>
                    </DialogHeader>
                    <Select value={selectedNewChat} onValueChange={(value) => setSelectedNewChat(value)}>
                      <SelectTrigger className={"w-60"}>
                        <SelectValue placeholder={"Select Product"} />
                      </SelectTrigger>
                      <SelectContent>
                        {getBotNames().map((botName, key) => {
                          return (
                            <SelectItem value={botName.toLowerCase()} key={key}>
                              {botName.toUpperCase()}
                            </SelectItem>
                          );
                        })}
                      </SelectContent>
                    </Select>
                    <InputWithLabel labelProps={{children: "Name"}} inputProps={{className: "w-full", onChange: (e)=>setChatName(e.target.value)}}  />
                    <DialogFooter>
                      <Button type="submit" onClick={handleAddBot}>
                        Add Bot
                      </Button>
                    </DialogFooter>
                  </DialogContent>
                </Dialog>
              </div>
              {renderTabs()}
            </TabsList>
            {renderTabsContent()}
          </Tabs>
          <Toaster />
        </CardContent>
      </Card>
      <InfoDialog dialogOpen={dialogOpen} onOpenChange={setDialogOpen} />
    </div>
  );
}

export default App;
