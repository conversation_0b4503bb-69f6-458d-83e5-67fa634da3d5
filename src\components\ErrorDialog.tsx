import {
  ModalDialog,
  ModalDialogAction,
  ModalDialogCancel,
  ModalDialogContent,
  ModalDialogDescription,
  ModalDialogFooter,
  ModalDialogHeader,
  ModalDialogTitle,
  Typography,
} from "dbh-reactui"
import type React from "react"

interface ErrorDialogProps {
  isOpen: boolean
  onOpenChange: (isOpen: boolean) => void
  errorMessage: string
}

const ErrorDialog: React.FC<ErrorDialogProps> = ({ isOpen, onOpenChange, errorMessage }) => {
  return (
    <ModalDialog open={isOpen} onOpenChange={() => onOpenChange(!isOpen)}>
      <ModalDialogContent>
        <ModalDialogHeader>
          <ModalDialogTitle>Ein unerwarteter Fehler ist aufgetreten</ModalDialogTitle>
          <ModalDialogDescription>
            Hier sind die Details zum Fehler:
          </ModalDialogDescription>
        </ModalDialogHeader>

        <div className="p-2">
          <Typography variant="caption_2" className="text-error">
            {errorMessage}
          </Typography>
        </div>

        <ModalDialogFooter>
          <ModalDialogCancel>Schließen</ModalDialogCancel>
          <ModalDialogAction onClick={() => onOpenChange(false)}>
            Verstanden
          </ModalDialogAction>
        </ModalDialogFooter>
      </ModalDialogContent>
    </ModalDialog>
  )
}

export default ErrorDialog
