export type User = {
  userId: string;
  username: string;
  email: string;
  emailVerified: boolean;
  name: string;
  firstname: string;
  lastname: string;
};
export type AskKiBotProps = { conversationId: string; question: string; productId: string; user?: User };
export type AskKiBotOnSuccessParams = {
  answers: Answer[];
  conversationId: string;
  createdAt: string;
  questionId: string;
  questionText: string;
};
export type OnErrorParams = { status: string | number; message: string };
export type RateKiBotProps = {
  productId: string;
  comment: string;
  conversationId: string;
  messageId: string;
  rating: number;
};
export type DeleteConversationProps = {
  clearOnly: boolean;
  conversationId: string;
  userId?: string
}
export type DeleteMessageProps = {
  dataId: string;
  userId?: string;
}
export type RateKiBotOnSuccessParams = { message: string; status: string };
export type KiBotInfoOnSuccessParams = { build_timestamp: string; service: string; version: string };
export type GetConversationsProps = { userId: string };
export type CreateConversationProps = {userId: string, productId: string, name: string}
export type ConversationsResponse = {
  success: boolean;
  conversations: Conversation[];
};
type Answer = {
  answerId: string;
  answerText: string;
  extend: string;
  paragraphNumbers: string;
  rating: number | null;
  comment: string;
  sections: string[];
  titles: string[];
  createdAt: string;
};
type Question = {
  questionId: string;
  questionText: string;
  answers: Answer[];
  createdAt: string;
};
export type Conversation = {
  conversationId: string;
  productId: string;
  language: string;
  questions: Question[];
  conversationCreatedAt: string;
  target: string;
  name: string;
};