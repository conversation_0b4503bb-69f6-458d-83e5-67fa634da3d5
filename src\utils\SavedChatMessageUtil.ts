import { Conversation } from "../types/api.ts";
import { ChatMessage } from "../types/chat.ts";

export class ChatMessageUtil {

   

  static groupByProduct(messages: ChatMessage[]): Record<string, ChatMessage[]> {
    return messages.reduce<Record<string, ChatMessage[]>>((acc, msg) => {
      const key = msg.productId;
      if (!acc[key]) acc[key] = [];
      acc[key].push(msg);
      return acc;
    }, {});
  }

  static transformConversationToSavedMessages(conv: Conversation): ChatMessage[] {
    const BOT_NAME = import.meta.env.VITE_NAME;
    const result: ChatMessage[] = [
      {productId: conv.productId,
        type: "answer",
        children: "Hallo ich bin " + BOT_NAME + ". Ich bin eine KI! Beachte das ich Fehler machen kann. Wie kann ich dir heute helfen?",
        conversationId: conv.conversationId,
        createdAt: "",
        conversationCreatedAt: conv.conversationCreatedAt,
        language: conv.language,
        messageId: 0,
        dataId: "",
        name: conv.name}
    ];
    let index = 0;

    conv.questions.forEach((question) => {
      index++;

      result.push({
        productId: conv.productId,
        type: "question",
        children: question.questionText,
        conversationId: conv.conversationId,
        createdAt: question.createdAt,
        conversationCreatedAt: conv.conversationCreatedAt,
        language: conv.language,
        messageId: index,
        dataId: question.questionId,
        name: conv.name
      });

      question.answers.forEach((answer) => {

        result.push({
          productId: conv.productId,
          type: "answer",
          children: answer.answerText,
          conversationId: conv.conversationId,
          dataId: answer.answerId,
          questionId: question.questionId,
          messageId: index,
          paragraphNumbers: answer.paragraphNumbers,
          conversationCreatedAt: conv.conversationCreatedAt,
          language: conv.language,
          createdAt: answer.createdAt,
          ...(answer.rating !== null && { rating: answer.rating }),
          ...(answer.comment && { comment: answer.comment }),
          sections: answer.sections,
          titles: answer.titles,
          extend: answer.extend,
          name: conv.name
        });
      });
    });

    return result;
  }
}
