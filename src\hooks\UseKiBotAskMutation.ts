import { useMutation, UseMutationResult } from "@tanstack/react-query";
import { getCurrentFormattedDate } from "../DateUtils.ts";
import { KiBotService } from "../KiBotService";
import { useChatMessageContext } from "../context/chat-messages-context.ts";
import { AskKiBotOnSuccessParams, AskKiBotProps, OnErrorParams, User } from "../types/api.ts";
import { ChatMessage } from "../types/chat.ts";
import { toast_error } from "dbh-reactui";

export type AskProps = { question: string; productId: string; name?: string };

function useKiBotAskMutation(
  productId: string,
  user: User,
  name: string,
  conversationId: string
) {
  const { savedChatMessages, setSavedChatMessages, setLoadingMessages } = useChatMessageContext();

  // 🧠 Nachricht senden (Frage stellen)
  async function askQuestion({
    question,
    productId,
  }: AskProps): Promise<AskKiBotOnSuccessParams> {
    const newMessage: ChatMessage = {
      productId,
      type: "question",
      createdAt: getCurrentFormattedDate(),
      children: question,
      language: "de",
      messageId: savedChatMessages[name]?.length || 0,
      name,
      conversationId,
    };

    // 🟢 Direkt im globalen State speichern
    setSavedChatMessages((prev) => ({
      ...prev,
      [name]: [...(prev[name] || []), newMessage],
    }));

    const askProps: AskKiBotProps = {
      conversationId,
      question,
      productId,
      user,
    };

    return await KiBotService.askKiBot(askProps);
  }

  // 🧩 Wenn Antwort erfolgreich kommt
  function handleSuccess(answer: AskKiBotOnSuccessParams) {
    const newAnswers: ChatMessage[] = answer.answers.map((ans, index) => ({
      productId,
      type: "answer",
      conversationId: answer.conversationId,
      dataId: ans.answerId,
      createdAt: getCurrentFormattedDate(),
      children: ans.answerText,
      messageId: (savedChatMessages[name]?.length || 0) + index,
      language: "de",
      name,
    }));



    setSavedChatMessages((prev) => {
      const newChatMessage = [
        ...prev[name].slice(0, -1),
        {
          ...prev[name][prev[name].length - 1],
          dataId: answer.questionId
        }
      ]

      return{
      ...prev,
      [name]: [...newChatMessage || [], ...newAnswers],
      }
    });
  }

  // ❌ Fehlerfall behandeln
  function handleError(error: OnErrorParams) {
    console.error("KiBot API Error:", error.message);
    toast_error(error.message);

    const newError: ChatMessage = {
      productId,
      type: "error",
      createdAt: getCurrentFormattedDate(),
      children: error.message,
      language: "de",
      messageId: savedChatMessages[name]?.length || 0,
      name,
      conversationId,
    };

    setSavedChatMessages((prev) => ({
      ...prev,
      [name]: [...(prev[name] || []), newError],
    }));
  }

  const mutation: UseMutationResult<
    AskKiBotOnSuccessParams,
    OnErrorParams,
    AskProps
  > = useMutation({
    mutationFn: askQuestion,
    onMutate: () => {
    setLoadingMessages(true);
    },
    onSettled: () => {
      setLoadingMessages(false);
    },
    onSuccess: handleSuccess,
    onError: handleError,
  });

  return { mutation };
}

export default useKiBotAskMutation;
