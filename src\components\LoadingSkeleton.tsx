import { LoadingSpinner, Skeleton } from "dbh-reactui";
import React from "react";

const LoadingSkeleton: React.FC = () => {
  return (
    <div className="relative flex w-full">
      <Skeleton width={300} height={80} animation="wave" variant="rounded" className="bg-[#E8F0F8]" />
      <div className="absolute ml-2 mt-2 flex items-center gap-2">
        <LoadingSpinner size="small" type="long" />
        <p className="animate-pulse italic text-[#333333]">Answering...</p>
      </div>
    </div>
  );
};

export default LoadingSkeleton;
