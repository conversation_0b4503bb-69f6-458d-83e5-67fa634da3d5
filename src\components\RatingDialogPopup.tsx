import React from "react";
import { Pop<PERSON>, PopoverContent, PopoverTrigger, Typography } from "dbh-reactui";
import <PERSON>Fill<PERSON>ogo from "../assets/star-fill.svg?react";
import InfoLogo from "../assets/information-line.svg?react";
import ThumpDownLogo from "../assets/thumb-down-line.svg?react";

const ForwardInfoLogo = React.forwardRef<HTMLDivElement, React.SVGProps<SVGSVGElement>>((props, ref) => (
  <div ref={ref}>
    <InfoLogo {...props} />
  </div>
));

const RatingDialogPopup: React.FC = () => {
  const renderStars = (numOfStars: number) => {
    return (
      <div className="flex w-32">
        {Array.from({ length: numOfStars }, (_, i) => {
          return <StarFillLogo key={i} className="h-6 w-6 fill-yellow-400" />;
        })}
      </div>
    );
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <ForwardInfoLogo className="h-5 w-5 cursor-pointer fill-primary-100" />
      </PopoverTrigger>
      <PopoverContent className="flex h-fit w-fit flex-col justify-center bg-gray-100 p-2">
        <div className="flex w-full">
          {renderStars(1)}
          <div className="flex items-center">
            <Typography variant="caption_1" className="text-primary-100">
              Falsche Antwort aber richtiges Thema
            </Typography>
          </div>
        </div>
        <div className="flex w-full">
          {renderStars(2)}
          <div className="flex items-center">
            <Typography variant="caption_1" className="text-primary-100">
              Meistens falsch, einige richtige Aspekte
            </Typography>
          </div>
        </div>
        <div className="flex w-full">
          {renderStars(3)}
          <div className="flex items-center">
            <Typography variant="caption_1" className="text-primary-100">
              Teilweise richtig, unvollständig oder unklar
            </Typography>
          </div>
        </div>
        <div className="flex w-full">
          {renderStars(4)}
          <div className="flex items-center">
            <Typography variant="caption_1" className="text-primary-100">
              Weitgehend korrekt, kleinere Fehler
            </Typography>
          </div>
        </div>
        <div className="flex w-full">
          {renderStars(5)}
          <div className="flex items-center">
            <Typography variant="caption_1" className="text-primary-100">
              Perfekt, vollständig und klar
            </Typography>
          </div>
        </div>
        <div className="flex w-full pt-6">
          <ThumpDownLogo className="h-6 w-6 fill-primary-100" />
          <div className="flex items-center pl-2">
            <Typography variant="caption_1" className="text-primary-100">
              "Schlechte Antwort": Die Antwort hat nichts mit der Frage oder dem Thema zu tun
            </Typography>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default RatingDialogPopup;
