export function getCurrentFormattedDate(): string {
  const now = new Date();
  const day = String(now.getDate()).padStart(2, "0");
  const month = String(now.getMonth() + 1).padStart(2, "0"); // Monate sind 0-indexiert
  const year = String(now.getFullYear()).slice(-2); // Nur die letzten zwei Ziffern des Jahres
  const hours = String(now.getHours()).padStart(2, "0");
  const minutes = String(now.getMinutes()).padStart(2, "0");
  const seconds = String(now.getSeconds()).padStart(2, "0");

  return `${day}.${month}.${year} - ${hours}:${minutes}:${seconds}`;
}
