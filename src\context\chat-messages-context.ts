import React, { createContext, useContext } from "react";
import { ChatMessage } from "../types/chat";

interface ChatMessagesContextType {
  savedChatMessages: Record<string, ChatMessage[]>;
  setSavedChatMessages: React.Dispatch<React.SetStateAction<Record<string, ChatMessage[]>>>;
  loadingMessages: boolean;
  setLoadingMessages: React.Dispatch<React.SetStateAction<boolean>>;
}


const ChatMessagesContext = createContext<ChatMessagesContextType | null>(null);

export const useChatMessageContext = () => {
  const context = useContext(ChatMessagesContext);
  if (!context) throw new Error("useChatMessageContext must be used within ChatProvider");
  return context;
};

export default ChatMessagesContext;
