import { toast_error } from "dbh-reactui";
import { OnErrorParams } from "../types/api";

const API_BASE = "/api";

export async function apiRequest<T>({
  path,
  method = "GET",
  body,
  headers = {},
  signal,
}: {
  path: string;
  method?: string;
  body?: unknown;
  headers?: Record<string, string>;
  signal?: AbortSignal;
}): Promise<T> {

  const response = await fetch(`${API_BASE}${path}`, {
    method,
    headers: {
      "Content-Type": "application/json",
      ...headers,
    },
    body: body ? JSON.stringify(body) : undefined,
    signal,
  });


  if (!response.ok) {
    let errorData: OnErrorParams = {
      status: response.status,
      message: response.statusText || "Unbekannter Fehler",
    };

    const contentType = response.headers.get("content-type");

    try {
      if (contentType?.includes("application/json")) {
        const json = await response.json();
        errorData = { ...errorData, ...json };
        if (!errorData.message) {
          errorData.message = "Unbekannter <PERSON>hler (JSON ohne message)";
        }
      } else {
        const text = await response.text();

        if (contentType?.includes("text/html")) {
          const match = text.match(/<h1>(.*?)<\/h1>/i);
          if (match) {
            errorData.message = match[1];
          } else {
            errorData.message = `HTTP Fehlerseite (Status ${response.status})`;
          }
        } else if (text) {
          errorData.message = text;
        }
      }
    } catch (parseError) {
      console.error("Fehler beim Parsen der Error-Response:", parseError);
      if (!errorData.message || errorData.message === "") {
        errorData.message = `Fehler beim Lesen der Antwort (Status ${response.status})`;
      }
    }

    toast_error(errorData.message)
    throw errorData;
  }

  return response.json() as Promise<T>;
}
