

export type BaseMessage={
    messageId: number;
    dataId?: string;
    type: "error" | "answer" | "question";
    children: string;
    createdAt: string;
    conversationId: string;
    conversationCreatedAt?: string;
    productId: string;
    language: string;
    name: string;
    questionId?: string;
}

export type Answer = (BaseMessage & {
    extend: string;
    paragraphNumbers: string;
    rating?: number;
    comment?: string;
    sections: string[];
    titles: string[];
})

export type ChatMessage = BaseMessage | Answer;



