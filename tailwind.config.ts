import { Config } from "tailwindcss";

export default {
  content: ["./src/**/*.{html,js,ts,jsx,tsx}"],
  theme: {
    extend: {
      colors: {
        primary: {
          100: "hsl(214, 33%, 37%)",
          200: "hsl(214, 33%, 30%)",
          300: "hsl(214, 33%, 20%)",
        },
        gray: {
          100: "var(--dbh-color-gray-100)",
          200: "var(--dbh-color-gray-200)",
          300: "var(--dbh-color-gray-300)",
          400: "var(--dbh-color-gray-400)",
        },
        accent: "var(--dbh-color-accent)",
        accentDark: "var(--dbh-color-accent-dark)",
        border: "var(--dbh-color-border)",
        white: "var(--dbh-color-white)",
        black: "var(--dbh-color-black)",
        muted: "var(--dbh-color-muted)",
        appbackground: "var(--dbh-color-appbackground)",
        elementbackground: "var(--dbh-color-elementbackground)",
        elementforeground: "var(--dbh-color-elementforeground)",
        success: "var(--dbh-color-success)",
        warning: "var(--dbh-color-warning)",
        information: "var(--dbh-color-information)",
        error: "hsl(356, 95%, 46%)",
        toast: {
          success: {
            background: "var(--dbh-toast-success-background)",
            border: "var(--dbh-toast-success-border)",
          },
          warning: {
            background: "var(--dbh-toast-warning-background)",
            border: "var(--dbh-toast-warning-border)",
          },
          error: {
            background: "var(--dbh-toast-error-background)",
            border: "var(--dbh-toast-error-border)",
          },
          info: {
            background: "var(--dbh-toast-info-background)",
            border: "var(--dbh-toast-info-border)",
          },
        },
        tooltip: {
          background: "var(--dbh-tooltip-background)",
          error: {
            border: "var(--dbh-tooltip-border-error)",
          },
        },
      },
      fontFamily: {
        dbh: ["var(--dbh-typeface)"],
      },
      fontSize: {
        caption2: ["var(--dbh-text-caption-2_size)", "var(--dbh-text-caption-2_height)"],
        caption1: ["var(--dbh-text-caption-1_size)", "var(--dbh-text-caption-1_height)"],
        body1: ["var(--dbh-text-body-1_size)", "var(--dbh-text-body-1_height)"],
        subtitle2: ["var(--dbh-text-subtitle-2_size)", "var(--dbh-text-subtitle-2_height)"],
        subtitle1: ["var(--dbh-text-subtitle-1_size)", "var(--dbh-text-subtitle-1_height)"],
        title3: ["var(--dbh-text-title-3_size)", "var(--dbh-text-title-3_height)"],
        title2: ["var(--dbh-text-title-2_size)", "var(--dbh-text-title-2_height)"],
        title1: ["var(--dbh-text-title-1_size)", "var(--dbh-text-title-1_height)"],
        titleLarge: ["var(--dbh-text-title-large_size)", "var(--dbh-text-title-large_height)"],
        display: ["var(--dbh-text-display_size)", "var(--dbh-text-display_height)"],
      },
      fontWeight: {
        caption2Strong: "var(--dbh-text-caption-2-strong_weight)",
        caption1Strong: "var(--dbh-text-caption-1-strong_weight)",
        caption1Stronger: "var(--dbh-text-caption-1-stronger_weight)",
        body1Strong: "var(--dbh-text-body-1-strong_weight)",
        body1Stronger: "var(--dbh-text-body-1-stronger_weight)",
        subtitle2: "var(--dbh-text-subtitle-2_weight)",
        subtitle2Stronger: "var(--dbh-text-subtitle-2-stronger_weight)",
        subtitle1: "var(--dbh-text-subtitle-1_weight)",
        title3: "var(--dbh-text-title-3_weight)",
        title2: "var(--dbh-text-title-2_weight)",
        title1: "var(--dbh-text-title-1_weight)",
        titleLarge: "var(--dbh-text-title-large_weight)",
        display: "var(--dbh-text-display_weight)",
      },
      boxShadow: {
        DEFAULT: "var(--dbh-shadow)",
        md: "var(--dbh-shadow-md)",
        lg: "var(--dbh-shadow-lg)",
      },
      borderRadius: {
        DEFAULT: "var(--dbh-rounded)",
        md: "var(--dbh-rounded-md)",
        lg: "var(--dbh-rounded-lg)",
        xl: "var(--dbh-rounded-xl)",
        full: "var(--dbh-rounded-full)",
      },
      transitionDuration: {
        short: "var(--dbh-transition-short)",
        medium: "var(--dbh-transition-medium)",
        large: "var(--dbh-transition-large)",
      },
      transitionTimingFunction: {
        DEFAULT: "var(--dbh-transition-timing)",
      },
      outlineOffset: {
        DEFAULT: "var(--dbh-outline-offset)",
      },
      outlineWidth: {
        DEFAULT: "var(--dbh-outline)",
      },
      borderWidth: {
        DEFAULT: "var(--dbh-border)",
        large: "var(--dbh-border-large)",
      },
    },
  },
  plugins: [],
} satisfies Config;
