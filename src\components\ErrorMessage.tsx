import { Button, Typography } from "dbh-reactui"

interface ErrorMessageProps {
    setOpenError: (state:boolean)=>void;
    onDelete: () => void;
    onRetry: ()=>void;
}

const ErrorMessage = ({setOpenError, onDelete, onRetry}: ErrorMessageProps)=>{

    return (
        <div className="mt-2 flex  flex-col gap-3 p-3  rounded-md bg-error bg-opacity-5">
            <Typography variant="caption_2" className="text-error">
                Es ist ein unerwarteter Fehler aufgetreten.{" "}
                <button
                onClick={() => setOpenError(true)}
                className="underline font-medium hover:text-error-dark"
                >
                Details anschauen
                </button>
            </Typography>

            <div className="flex flex-col gap-2">
                <div className="flex gap-2">
                <Button
                    variant="primary"
                    
                    onClick={onRetry}
                >
                    Erneut versuchen
                </Button>
                <Button
                    variant="error"
                    
                    onClick={onDelete}
                >
                    Löschen
                </Button>
                </div>
            </div>
        </div>
    )
}

export default ErrorMessage