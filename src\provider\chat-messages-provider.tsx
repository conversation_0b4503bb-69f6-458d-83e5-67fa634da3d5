import { useQuery } from "@tanstack/react-query";
import React, { ReactNode, useEffect, useState } from "react";
import { useAuth } from "react-oidc-context";
import ChatMessagesContext from "../context/chat-messages-context.ts";
import { KiBotService } from "../KiBotService.ts";
import { Conversation } from "../types/api.ts";
import { ChatMessage } from "../types/chat.ts";
import { ChatMessageUtil } from "../utils/SavedChatMessageUtil.ts";

export const ChatMessagesProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const auth = useAuth();
  
  const userId = auth.user ? auth.user.profile.sub : "";
  const [savedChatMessages, setSavedChatMessages] = useState<Record<string, ChatMessage[]>>({});
  const [loadingMessages, setLoadingMessages] = useState<boolean>(false);
type ConversationsResponse = {
  success: boolean;
  conversations: Conversation[];
};

const { data } = useQuery<ConversationsResponse, Error, Record<string, ChatMessage[]>>({
  queryKey: ["getConversationsForUserQuery", userId],
  queryFn: () => KiBotService.getConversationsForUser({ userId: userId }),
  select: (res) => transformQueryData(res.conversations),
  enabled: !!userId,
});



function transformQueryData(allConversations: Conversation[]): Record<string, ChatMessage[]> {
  const result: Record<string, ChatMessage[]> = {};

  allConversations.forEach((conv) => {
    result[conv.name] = ChatMessageUtil.transformConversationToSavedMessages(conv);
  });

  return result;
}


  useEffect(() => {
    if (data) {
      setSavedChatMessages(data);
    }
  }, [data]);

  return (
    <ChatMessagesContext.Provider value={{ savedChatMessages, setSavedChatMessages, loadingMessages, setLoadingMessages }}>
      {children}
    </ChatMessagesContext.Provider>
  );
};

export default ChatMessagesProvider;
